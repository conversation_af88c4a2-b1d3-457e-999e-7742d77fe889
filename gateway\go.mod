module github.com/olzzhas/edunite-server/gateway

go 1.23.0

require (
	github.com/Nerzal/gocloak/v13 v13.9.0
	github.com/gin-contrib/cors v1.7.2
	github.com/gin-gonic/gin v1.10.0
	github.com/joho/godotenv v1.5.1
	github.com/olzzhas/edunite-server/analytics_service v0.0.0-20250612235843-d8116ee3b0c8
	github.com/olzzhas/edunite-server/auth_service v0.0.0-00010101000000-000000000000
	github.com/olzzhas/edunite-server/course_service v0.0.0-20250423230256-2ea2c12c537b
	github.com/olzzhas/edunite-server/notification_service v0.0.0-20250612182403-53af9838535b
	github.com/olzzhas/edunite-server/sport_service v0.0.0-20250427084811-aa213a9e0371
	github.com/olzzhas/edunite-server/storage_service v0.0.0-20241216115010-7cb54c76121f
	github.com/olzzhas/edunite-server/user_service v0.0.0-20250424154154-bcd80b352b2a
	github.com/prometheus/client_golang v1.17.0
	github.com/streadway/amqp v1.1.0
	github.com/stretchr/testify v1.9.0
	google.golang.org/grpc v1.72.0
	google.golang.org/protobuf v1.36.5
)

require (
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/bytedance/sonic v1.11.6 // indirect
	github.com/bytedance/sonic/loader v0.1.1 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/cloudwego/base64x v0.1.4 // indirect
	github.com/cloudwego/iasm v0.2.0 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/gabriel-vasile/mimetype v1.4.3 // indirect
	github.com/gin-contrib/sse v0.1.0 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-playground/validator/v10 v10.20.0 // indirect
	github.com/go-resty/resty/v2 v2.7.0 // indirect
	github.com/goccy/go-json v0.10.3 // indirect
	github.com/golang-jwt/jwt/v5 v5.2.2 // indirect
	github.com/golang/protobuf v1.5.4 // indirect
	github.com/grpc-ecosystem/grpc-gateway/v2 v2.22.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/klauspost/cpuid/v2 v2.2.8 // indirect
	github.com/kr/text v0.2.0 // indirect
	github.com/leodido/go-urn v1.4.0 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/matttproud/golang_protobuf_extensions v1.0.4 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/opentracing/opentracing-go v1.2.0 // indirect
	github.com/pelletier/go-toml/v2 v2.2.2 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/prometheus/client_model v0.4.1-0.20230718164431-9a2bf3000d16 // indirect
	github.com/prometheus/common v0.44.0 // indirect
	github.com/prometheus/procfs v0.11.1 // indirect
	github.com/rogpeppe/go-internal v1.13.1 // indirect
	github.com/segmentio/ksuid v1.0.4 // indirect
	github.com/stretchr/objx v0.5.2 // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	github.com/ugorji/go/codec v1.2.12 // indirect
	golang.org/x/arch v0.8.0 // indirect
	golang.org/x/crypto v0.33.0 // indirect
	golang.org/x/net v0.35.0 // indirect
	golang.org/x/sys v0.30.0 // indirect
	golang.org/x/text v0.22.0 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20250218202821-56aae31c358a // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20250218202821-56aae31c358a // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)

replace github.com/olzzhas/edunite-server/sport_service => ../sport_service

replace github.com/olzzhas/edunite-server/course_service => ../course_service

replace github.com/olzzhas/edunite-server/user_service => ../user_service

replace github.com/olzzhas/edunite-server/storage_service => ../storage_service

replace github.com/olzzhas/edunite-server/auth_service => ../auth_service

replace github.com/olzzhas/edunite-server/notification_service => ../notification_service

replace github.com/olzzhas/edunite-server/analytics_service => ../analytics_service
