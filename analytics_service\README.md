# Analytics Service

Analytics Service предоставляет аналитические данные и статистику для платформы EduNite. Сервис реализован как gRPC сервер и предоставляет данные для Dashboard и Analytics страниц.

## Возможности

- **Dashboard Overview**: Основные метрики (общее количество пользователей, курсов, потоков, активных студентов)
- **Users Timeline**: Временные ряды активности пользователей
- **User Demographics**: Демографические данные пользователей (устройства, локации)
- **Recent Activities**: Последние активности пользователей (регистрации, отправка заданий)
- **Detailed Stats**: Детальная статистика (регистрации на курсы, активность пользователей, источники трафика)
- **Performance Metrics**: Ключевые метрики производительности (процент завершения курсов, средние оценки, посещаемость)

## Архитектура

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Gateway       │───▶│ Analytics       │───▶│   PostgreSQL    │
│   (HTTP API)    │    │   Service       │    │   Database      │
│                 │    │   (gRPC)        │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Установка и запуск

### Локальная разработка

1. **Установите зависимости:**
```bash
go mod tidy
```

2. **Сгенерируйте protobuf файлы:**
```bash
make proto
```

3. **Соберите сервис:**
```bash
make build
```

4. **Запустите сервис:**
```bash
make run
```

### Docker

1. **Соберите Docker образ:**
```bash
docker build -t analytics-service .
```

2. **Запустите контейнер:**
```bash
docker run -p 50055:50055 \
  -e DATABASE_URL="************************************/dbname" \
  analytics-service
```

### Docker Compose

Сервис автоматически запускается как часть общей инфраструктуры:

```bash
docker-compose up analytics_service
```

## Конфигурация

Сервис настраивается через переменные окружения:

| Переменная | Описание | По умолчанию |
|------------|----------|--------------|
| `ANALYTICS_SERVICE_PORT` | Порт gRPC сервера | `50055` |
| `DATABASE_URL` | URL подключения к PostgreSQL | - |
| `DB_HOST` | Хост базы данных | `***************` |
| `DB_PORT` | Порт базы данных | `5432` |
| `DB_NAME` | Имя базы данных | `edunite` |
| `DB_USER` | Пользователь базы данных | `olzzhas` |
| `DB_PASSWORD` | Пароль базы данных | - |

## API

Сервис предоставляет следующие gRPC методы:

- `GetOverview` - Основные метрики
- `GetUsersTimeline` - Временные ряды пользователей
- `GetUserDemographics` - Демографические данные
- `GetRecentActivities` - Последние активности
- `GetDetailedStats` - Детальная статистика
- `GetPerformanceMetrics` - Метрики производительности

HTTP API доступен через Gateway на `/api/analytics/*`. См. [Analytics API Documentation](../docs/analytics-api.md).

## Разработка

### Структура проекта

```
analytics_service/
├── cmd/
│   └── main.go              # Точка входа
├── internal/
│   ├── config/              # Конфигурация
│   ├── database/            # Подключение к БД
│   ├── repository/          # Слой данных
│   └── service/             # Бизнес-логика
├── pb/                      # Сгенерированные protobuf файлы
├── proto/                   # Protobuf схемы
├── Dockerfile               # Docker образ
├── Makefile                 # Команды сборки
└── README.md               # Документация
```

### Команды Make

- `make proto` - Генерация protobuf файлов
- `make build` - Сборка сервиса
- `make run` - Запуск сервиса
- `make clean` - Очистка сгенерированных файлов
- `make test` - Запуск тестов
- `make dev` - Запуск в режиме разработки

### Добавление новых метрик

1. Обновите `proto/analytics.proto`
2. Сгенерируйте protobuf файлы: `make proto`
3. Добавьте методы в `internal/repository/analytics_repository.go`
4. Реализуйте gRPC методы в `internal/service/analytics_service.go`
5. Добавьте HTTP обработчики в Gateway

## Мониторинг

Сервис интегрируется с системой мониторинга EduNite:

- **Prometheus**: Метрики производительности (при добавлении HTTP endpoint)
- **Grafana**: Визуализация аналитических данных
- **Логирование**: Структурированные логи

## Производительность

- **Кэширование**: Результаты запросов кэшируются для улучшения производительности
- **Индексы БД**: Оптимизированные индексы для аналитических запросов
- **Пагинация**: Поддержка пагинации для больших наборов данных

## Безопасность

- **Аутентификация**: Все запросы требуют валидного токена
- **Авторизация**: Доступ к аналитике только для администраторов
- **Валидация**: Проверка всех входных параметров

## Тестирование

Используйте файл `test_analytics_api.http` для тестирования API:

```bash
# Запустите сервисы
docker-compose up

# Выполните тестовые запросы
# Используйте VS Code REST Client или аналогичный инструмент
```
