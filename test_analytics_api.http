### Analytics API Testing

### 1. Get Overview Statistics
GET http://localhost:8081/api/analytics/overview
Content-Type: application/json

###

### 2. Get Overview Statistics with period
GET http://localhost:8081/api/analytics/overview?period=last_30_days
Content-Type: application/json

###

### 3. Get Users Timeline
GET http://localhost:8081/api/analytics/users-timeline
Content-Type: application/json

###

### 4. Get Users Timeline with parameters
GET http://localhost:8081/api/analytics/users-timeline?period=30d&granularity=day
Content-Type: application/json

###

### 5. Get User Demographics
GET http://localhost:8081/api/analytics/user-demographics
Content-Type: application/json

###

### 6. Get Recent Activities
GET http://localhost:8081/api/analytics/recent-activities
Content-Type: application/json

###

### 7. Get Recent Activities with parameters
GET http://localhost:8081/api/analytics/recent-activities?limit=5&type=all
Content-Type: application/json

###

### 8. Get Detailed Stats
GET http://localhost:8081/api/analytics/detailed-stats
Content-Type: application/json

###

### 9. Get Detailed Stats with parameters
GET http://localhost:8081/api/analytics/detailed-stats?period=30d&metrics=activity,performance
Content-Type: application/json

###

### 10. Get Performance Metrics
GET http://localhost:8081/api/analytics/performance-metrics
Content-Type: application/json

###
