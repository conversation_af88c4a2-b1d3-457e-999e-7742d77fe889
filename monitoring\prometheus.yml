global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alerts.yml"

scrape_configs:
  # Prometheus сам себя
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Gateway service - основной источник метрик
  - job_name: 'gateway'
    static_configs:
      - targets: ['host.docker.internal:8081']  # Используем host.docker.internal для доступа к локальному Gateway
    metrics_path: '/metrics'
    scrape_interval: 15s

  # gRPC services не экспортируют HTTP метрики напрямую
  # Метрики будут собираться через Gateway

  # User service - только если добавите HTTP endpoint для метрик
  # - job_name: 'user-service'
  #   static_configs:
  #     - targets: ['user_service:50051']
  #   metrics_path: '/metrics'
  #   scrape_interval: 30s

  # Auth service - только если добавите HTTP endpoint для метрик
  # - job_name: 'auth-service'
  #   static_configs:
  #     - targets: ['auth_service:50052']
  #   metrics_path: '/metrics'
  #   scrape_interval: 30s

  # Course service - только если добавите HTTP endpoint для метрик
  # - job_name: 'course-service'
  #   static_configs:
  #     - targets: ['course_service:50053']
  #   metrics_path: '/metrics'
  #   scrape_interval: 30s

  # Sport service - только если добавите HTTP endpoint для метрик
  # - job_name: 'sport-service'
  #   static_configs:
  #     - targets: ['sport_service:50054']
  #   metrics_path: '/metrics'
  #   scrape_interval: 30s

  # Storage service - только если добавите HTTP endpoint для метрик
  # - job_name: 'storage-service'
  #   static_configs:
  #     - targets: ['storage_service:50059']
  #   metrics_path: '/metrics'
  #   scrape_interval: 30s

  # Notification service - только если добавите HTTP endpoint для метрик
  # - job_name: 'notification-service'
  #   static_configs:
  #     - targets: ['notification_service:50056']
  #   metrics_path: '/metrics'
  #   scrape_interval: 30s

  # Analytics service - только если добавите HTTP endpoint для метрик
  # - job_name: 'analytics-service'
  #   static_configs:
  #     - targets: ['analytics_service:50055']
  #   metrics_path: '/metrics'
  #   scrape_interval: 30s

  # PostgreSQL (если есть postgres_exporter)
  # - job_name: 'postgres'
  #   static_configs:
  #     - targets: ['postgres_exporter:9187']

  # RabbitMQ (встроенные метрики)
  - job_name: 'rabbitmq'
    static_configs:
      - targets: ['rabbitmq:15692']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # RabbitMQ Management API метрики
  - job_name: 'rabbitmq-management'
    static_configs:
      - targets: ['rabbitmq:15672']
    metrics_path: '/api/metrics'
    scrape_interval: 30s
    basic_auth:
      username: 'guest'
      password: 'guest'

  # MongoDB (если есть mongodb_exporter)
  # - job_name: 'mongodb'
  #   static_configs:
  #     - targets: ['mongodb_exporter:9216']

  # MinIO (встроенные метрики)
  - job_name: 'minio'
    static_configs:
      - targets: ['minio:9000']
    metrics_path: '/minio/v2/metrics/cluster'
    scrape_interval: 30s

  # Redis (если есть redis_exporter)
  # - job_name: 'redis'
  #   static_configs:
  #     - targets: ['redis_exporter:9121']
